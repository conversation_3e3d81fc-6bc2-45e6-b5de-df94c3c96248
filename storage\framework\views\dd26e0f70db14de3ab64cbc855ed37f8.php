<?php
    $record = $getRecord();
    $journal = $record->journal;
    $journalEntries = $journal ? $journal->journalEntries()->with('account')->get() : collect();
?>

<?php if($journal && $journalEntries->count() > 0): ?>
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4">
        
        <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-3">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Journal Entries</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($journalEntries->count()); ?> entries</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <?php
                    $totalDebit = $journalEntries->sum('debit');
                    $totalCredit = $journalEntries->sum('credit');
                    $isBalanced = abs($totalDebit - $totalCredit) < 0.01;
                ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($isBalanced ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'); ?>">
                    <?php echo e($isBalanced ? 'Balanced' : 'Unbalanced'); ?>

                </span>
            </div>
        </div>

        
        <div class="overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-100 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Akun
                        </th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Deskripsi
                        </th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Debit
                        </th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Kredit
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php $__currentLoopData = $journalEntries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-4 py-3 whitespace-nowrap">
                                <div class="flex flex-col">
                                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        <?php echo e($entry->account->kode_akun ?? 'N/A'); ?>

                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        <?php echo e($entry->account->nama_akun ?? 'Unknown Account'); ?>

                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm text-gray-900 dark:text-gray-100">
                                    <?php echo e($entry->description); ?>

                                </div>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-right">
                                <?php if($entry->debit > 0): ?>
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        Rp <?php echo e(number_format($entry->debit, 0, ',', '.')); ?>

                                    </span>
                                <?php else: ?>
                                    <span class="text-sm text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-right">
                                <?php if($entry->credit > 0): ?>
                                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        Rp <?php echo e(number_format($entry->credit, 0, ',', '.')); ?>

                                    </span>
                                <?php else: ?>
                                    <span class="text-sm text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
                <tfoot class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <td colspan="2" class="px-4 py-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                            Total
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-bold text-gray-900 dark:text-gray-100">
                            Rp <?php echo e(number_format($totalDebit, 0, ',', '.')); ?>

                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-bold text-gray-900 dark:text-gray-100">
                            Rp <?php echo e(number_format($totalCredit, 0, ',', '.')); ?>

                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>

        
        <div class="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Journal dibuat: <?php echo e($journal->created_at->format('d M Y H:i')); ?>

            </div>
            <div class="flex items-center space-x-2">
                <?php if($journal->status === 'Posted'): ?>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Posted
                    </span>
                <?php endif; ?>
                
                <a href="<?php echo e(route('filament.admin.resources.journals.view', ['record' => $journal->id])); ?>" 
                   target="_blank"
                   class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                    Lihat Journal
                </a>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="text-center py-8">
        <div class="flex flex-col items-center">
            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Belum Ada Journal Entry</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 max-w-sm">
                Journal entry akan dibuat otomatis ketika expense request ini dibayar dan metode pembayaran dipilih.
            </p>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\laragon\www\lrp\resources\views\infolists\components\journal-entries-display.blade.php ENDPATH**/ ?>