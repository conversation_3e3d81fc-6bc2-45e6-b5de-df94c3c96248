<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <?php echo e($this->form); ?>


            <div class="mt-6 flex justify-end">
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']); ?>
                    Export PDF
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Income Statement Report -->
        <?php if($this->start_date && $this->end_date): ?>
            <?php
                $data = $this->getIncomeStatementData();
            ?>

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        PT. LINTAS RIAU PRIMA
                    </h2>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
                        LAPORAN RUGI LABA
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        <?php echo e($data['periode']['title']); ?>

                    </p>
                </div>

                <div class="space-y-8">
                    <!-- PENDAPATAN -->
                    <div>
                        <h3
                            class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                            PENDAPATAN
                        </h3>

                        <?php if(count($data['revenues']) > 0): ?>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $data['revenues']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $revenue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center py-1">
                                        <span class="text-gray-700 dark:text-gray-300">
                                            <?php echo e($revenue['account']->kode_akun); ?> - <?php echo e($revenue['account']->nama_akun); ?>

                                        </span>
                                        <span class="font-medium text-gray-900 dark:text-gray-100">
                                            Rp <?php echo e(number_format($revenue['balance'], 0, ',', '.')); ?>

                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <div
                                    class="flex justify-between items-center py-3 border-t-2 border-gray-300 dark:border-gray-600 font-bold text-lg">
                                    <span class="text-gray-900 dark:text-gray-100">TOTAL PENDAPATAN</span>
                                    <span class="text-green-600 dark:text-green-400">
                                        Rp <?php echo e(number_format($data['total_revenue'], 0, ',', '.')); ?>

                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500 dark:text-gray-400 italic">Tidak ada data pendapatan</p>
                        <?php endif; ?>
                    </div>

                    <!-- BEBAN -->
                    <div>
                        <h3
                            class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                            BEBAN
                        </h3>

                        <?php if(count($data['expenses']) > 0): ?>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $data['expenses']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center py-1">
                                        <span class="text-gray-700 dark:text-gray-300">
                                            <?php echo e($expense['account']->kode_akun); ?> - <?php echo e($expense['account']->nama_akun); ?>

                                        </span>
                                        <span class="font-medium text-gray-900 dark:text-gray-100">
                                            Rp <?php echo e(number_format($expense['balance'], 0, ',', '.')); ?>

                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <div
                                    class="flex justify-between items-center py-3 border-t-2 border-gray-300 dark:border-gray-600 font-bold text-lg">
                                    <span class="text-gray-900 dark:text-gray-100">TOTAL BEBAN</span>
                                    <span class="text-red-600 dark:text-red-400">
                                        Rp <?php echo e(number_format($data['total_expense'], 0, ',', '.')); ?>

                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500 dark:text-gray-400 italic">Tidak ada data beban</p>
                        <?php endif; ?>
                    </div>

                    <!-- LABA BERSIH -->
                    <div class="pt-6 border-t-4 border-gray-400 dark:border-gray-600">
                        <div class="flex justify-between items-center py-4 bg-gray-50 dark:bg-gray-700 px-6 rounded-lg">
                            <span class="text-xl font-bold text-gray-900 dark:text-gray-100">
                                LABA BERSIH
                            </span>
                            <span
                                class="text-2xl font-bold <?php echo e($data['net_income'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                                Rp <?php echo e(number_format($data['net_income'], 0, ',', '.')); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center py-8">
                    <div class="text-gray-400 dark:text-gray-500 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                        Pilih Periode Laporan
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        Silakan pilih tanggal mulai dan akhir untuk melihat laporan laba rugi
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\lrp\resources\views\filament\pages\income-statement-old.blade.php ENDPATH**/ ?>