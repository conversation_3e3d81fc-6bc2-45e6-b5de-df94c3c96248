<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\Akun;

class NewInvoicePostingRulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        // Delete existing invoice posting rules
        PostingRule::where('source_type', 'Invoice')->delete();

        $this->createInvoicePostingRules();
    }

    private function createInvoicePostingRules()
    {
        // Get required accounts based on new COA
        $accounts = [
            'piutang_usaha' => Akun::where('kode_akun', '200.1')->first(), // Piutang Usaha PT. ABC
            'penjualan_bbm' => Akun::where('kode_akun', '400')->first(), // Penjualan BBM
            'oat_penjualan' => Akun::where('kode_akun', '401')->first(), // OAT Penjualan BBM
            'pbbkb' => Akun::where('kode_akun', '402')->first(), // PBBKB
            'pendapatan_jasa_angkut' => Akun::where('kode_akun', '403')->first(), // Pendapatan Jasa Angkut
            'hpp_bbm' => Akun::where('kode_akun', '507')->first(), // HPP BBM
            'hpp_jasa_angkut' => Akun::where('kode_akun', '506')->first(), // HPP Jasa Angkut APMS
            'persediaan_awal' => Akun::where('kode_akun', '500')->first(), // Persediaan Awal
            'persediaan_akhir' => Akun::where('kode_akun', '504')->first(), // Persediaan Akhir
            'cash' => Akun::where('kode_akun', '100')->first(), // Cash in Hand
            'bank_mandiri' => Akun::where('kode_akun', '101')->first(), // Giro Bank Mandiri
            'bank_bni' => Akun::where('kode_akun', '102')->first(), // Giro Bank BNI
            'bank_bni_usd' => Akun::where('kode_akun', '103')->first(), // Giro USD BNI
            'bank_cimb' => Akun::where('kode_akun', '104')->first(), // Giro Bank CIMB Niaga
            'bank_bsi' => Akun::where('kode_akun', '105')->first(), // Giro Bank BSI
        ];

        // Validate required accounts
        if (!$accounts['piutang_usaha'] || !$accounts['penjualan_bbm']) {
            $this->command->error('Required accounts not found. Please run CoaSeeder first.');
            return;
        }

        $this->createInvoiceIssuanceRules($accounts);
        $this->createInvoicePaymentRules($accounts);
        $this->createHPPRules($accounts);

        $this->command->info('✅ Invoice Posting Rules created successfully!');
    }

    private function createInvoiceIssuanceRules($accounts)
    {
        // 1. Posting Rule untuk Penerbitan Invoice BBM
        $invoiceBBMRule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - Penjualan BBM',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'create', 'type' => 'bbm'],
            'description' => 'Auto posting ketika invoice BBM diterbitkan',
            'is_active' => true,
            'priority' => 1,
            'created_by' => 1,
        ]);

        // Debit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceBBMRule->id,
            'account_id' => $accounts['piutang_usaha']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang dari invoice BBM {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Penjualan BBM
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceBBMRule->id,
            'account_id' => $accounts['penjualan_bbm']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Penjualan BBM dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);

        // 2. Posting Rule untuk Penerbitan Invoice Jasa Angkut
        $invoiceJasaRule = PostingRule::create([
            'rule_name' => 'Penerbitan Invoice - Jasa Angkut',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'create', 'type' => 'jasa_angkut'],
            'description' => 'Auto posting ketika invoice jasa angkut diterbitkan',
            'is_active' => true,
            'priority' => 2,
            'created_by' => 1,
        ]);

        // Debit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceJasaRule->id,
            'account_id' => $accounts['piutang_usaha']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'total_invoice',
            'description_template' => 'Piutang dari invoice jasa angkut {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Pendapatan Jasa Angkut
        PostingRuleEntry::create([
            'posting_rule_id' => $invoiceJasaRule->id,
            'account_id' => $accounts['pendapatan_jasa_angkut']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'subtotal',
            'description_template' => 'Pendapatan jasa angkut dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);
    }

    private function createInvoicePaymentRules($accounts)
    {
        // 3. Posting Rule untuk Pembayaran Invoice - Cash
        $paymentCashRule = PostingRule::create([
            'rule_name' => 'Pembayaran Invoice - Cash',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'payment', 'payment_method' => 'cash'],
            'description' => 'Auto posting ketika invoice dibayar dengan cash',
            'is_active' => true,
            'priority' => 3,
            'created_by' => 1,
        ]);

        // Debit: Cash
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentCashRule->id,
            'account_id' => $accounts['cash']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'paid_amount',
            'description_template' => 'Penerimaan cash dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentCashRule->id,
            'account_id' => $accounts['piutang_usaha']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'paid_amount',
            'description_template' => 'Pelunasan piutang invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);

        // 4. Posting Rule untuk Pembayaran Invoice - Bank Transfer
        $paymentBankRule = PostingRule::create([
            'rule_name' => 'Pembayaran Invoice - Bank Transfer',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'payment', 'payment_method' => 'bank_transfer'],
            'description' => 'Auto posting ketika invoice dibayar dengan bank transfer',
            'is_active' => true,
            'priority' => 4,
            'created_by' => 1,
        ]);

        // Debit: Bank (default to Mandiri)
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentBankRule->id,
            'account_id' => $accounts['bank_mandiri']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'SourceValue',
            'source_property' => 'paid_amount',
            'description_template' => 'Penerimaan bank transfer dari invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 1,
        ]);

        // Credit: Piutang Usaha
        PostingRuleEntry::create([
            'posting_rule_id' => $paymentBankRule->id,
            'account_id' => $accounts['piutang_usaha']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'SourceValue',
            'source_property' => 'paid_amount',
            'description_template' => 'Pelunasan piutang invoice {source.nomor_invoice} - {source.nama_pelanggan}',
            'sort_order' => 2,
        ]);
    }

    private function createHPPRules($accounts)
    {
        // 5. Posting Rule untuk HPP BBM
        $hppBBMRule = PostingRule::create([
            'rule_name' => 'HPP BBM dari Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'create', 'type' => 'bbm'],
            'description' => 'Auto posting untuk mencatat HPP BBM ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 5,
            'created_by' => 1,
        ]);

        // Debit: HPP BBM
        PostingRuleEntry::create([
            'posting_rule_id' => $hppBBMRule->id,
            'account_id' => $accounts['hpp_bbm']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'invoice_details.sum(volume * harga_beli)',
            'description_template' => 'HPP BBM dari invoice {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Persediaan BBM
        PostingRuleEntry::create([
            'posting_rule_id' => $hppBBMRule->id,
            'account_id' => $accounts['persediaan_akhir']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'invoice_details.sum(volume * harga_beli)',
            'description_template' => 'Pengurangan persediaan BBM dari invoice {source.nomor_invoice}',
            'sort_order' => 2,
        ]);

        // 6. Posting Rule untuk HPP Jasa Angkut
        $hppJasaRule = PostingRule::create([
            'rule_name' => 'HPP Jasa Angkut dari Invoice',
            'source_type' => 'Invoice',
            'trigger_condition' => ['action' => 'create', 'type' => 'jasa_angkut'],
            'description' => 'Auto posting untuk mencatat HPP jasa angkut ketika invoice diterbitkan',
            'is_active' => true,
            'priority' => 6,
            'created_by' => 1,
        ]);

        // Debit: HPP Jasa Angkut
        PostingRuleEntry::create([
            'posting_rule_id' => $hppJasaRule->id,
            'account_id' => $accounts['hpp_jasa_angkut']->id,
            'dc_type' => 'Debit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'invoice_details.sum(biaya_operasional)',
            'description_template' => 'HPP jasa angkut dari invoice {source.nomor_invoice}',
            'sort_order' => 1,
        ]);

        // Credit: Kas (untuk biaya operasional yang sudah dikeluarkan)
        PostingRuleEntry::create([
            'posting_rule_id' => $hppJasaRule->id,
            'account_id' => $accounts['cash']->id,
            'dc_type' => 'Credit',
            'amount_type' => 'Calculated',
            'calculation_expression' => 'invoice_details.sum(biaya_operasional)',
            'description_template' => 'Biaya operasional jasa angkut dari invoice {source.nomor_invoice}',
            'sort_order' => 2,
        ]);
    }
}
