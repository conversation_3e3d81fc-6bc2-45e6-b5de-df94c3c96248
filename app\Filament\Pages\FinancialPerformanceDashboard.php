<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\DeliveryOrder;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;

class FinancialPerformanceDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
    protected static ?string $navigationLabel = 'Financial Performance';
    protected static ?string $title = 'Dashboard Financial Performance';
    protected static string $view = 'filament.pages.financial-performance-dashboard';
    protected static ?int $navigationSort = 9;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    public $startDate = null;
    public $endDate = null;

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_month';
        $this->startDate = $this->startDate ?? now()->startOfMonth()->format('Y-m-d');
        $this->endDate = $this->endDate ?? now()->endOfMonth()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_month')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn () => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn () => $this->selectedPeriod === 'custom')
                    ->live(),
            ])
            ->columns(3);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();
        
        match ($period) {
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'last_month' => [
                $this->startDate = $now->subMonth()->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            'current_year' => [
                $this->startDate = $now->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getFinancialKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // Revenue
        $totalRevenue = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        // Costs (estimated)
        $operationalCosts = $totalRevenue * 0.15; // 15% operational costs
        $deliveryCosts = DB::table('delivery_order')
            ->whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->sum('biaya_sewa_jasa') ?? 0;
        $adminCosts = $totalRevenue * 0.05; // 5% admin costs
        $totalCosts = $operationalCosts + $deliveryCosts + $adminCosts;

        // Profit & Loss
        $grossProfit = $totalRevenue - ($totalRevenue * 0.70); // Assuming 70% COGS
        $netProfit = $totalRevenue - $totalCosts;
        $profitMargin = $totalRevenue > 0 ? round(($netProfit / $totalRevenue) * 100, 2) : 0;

        // Cash Flow (simplified)
        $cashInflow = DB::table('delivery_order')
            ->whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        $cashOutflow = $totalCosts;
        $netCashFlow = $cashInflow - $cashOutflow;

        // Outstanding Receivables
        $outstandingReceivables = DB::table('delivery_order')
            ->whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->whereIn('payment_status', ['pending', 'partial'])
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        return [
            'total_revenue' => $totalRevenue,
            'gross_profit' => $grossProfit,
            'net_profit' => $netProfit,
            'profit_margin' => $profitMargin,
            'total_costs' => $totalCosts,
            'operational_costs' => $operationalCosts,
            'delivery_costs' => $deliveryCosts,
            'admin_costs' => $adminCosts,
            'cash_inflow' => $cashInflow,
            'cash_outflow' => $cashOutflow,
            'net_cash_flow' => $netCashFlow,
            'outstanding_receivables' => $outstandingReceivables,
        ];
    }

    public function getMonthlyProfitLossData(): array
    {
        $endDate = Carbon::parse($this->endDate);
        $startDate = $endDate->copy()->subMonths(11)->startOfMonth();

        return DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->select([
                DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m") as month'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as revenue'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.70) as cogs'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.30) as gross_profit'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.20) as operating_expenses'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.10) as net_profit'),
            ])
            ->groupBy(DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    public function getCashFlowData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // Daily cash flow for the selected period
        $dailyCashFlow = [];
        $current = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);

        while ($current <= $end) {
            $dailyInflow = DB::table('delivery_order')
                ->whereDate('tanggal_delivery', $current)
                ->where('payment_status', 'paid')
                ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
                ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
                ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual')) ?? 0;

            $dailyOutflow = DB::table('delivery_order')
                ->whereDate('tanggal_delivery', $current)
                ->sum('biaya_sewa_jasa') ?? 0;

            $dailyCashFlow[] = [
                'date' => $current->format('Y-m-d'),
                'inflow' => $dailyInflow,
                'outflow' => $dailyOutflow,
                'net_flow' => $dailyInflow - $dailyOutflow,
            ];

            $current->addDay();
        }

        return $dailyCashFlow;
    }

    public function getCostBreakdownData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $totalRevenue = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        $deliveryCosts = DB::table('delivery_order')
            ->whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->sum('biaya_sewa_jasa') ?? 0;

        return [
            'cogs' => $totalRevenue * 0.70,
            'operational_costs' => $totalRevenue * 0.15,
            'delivery_costs' => $deliveryCosts,
            'admin_costs' => $totalRevenue * 0.05,
            'marketing_costs' => $totalRevenue * 0.03,
            'other_costs' => $totalRevenue * 0.02,
        ];
    }

    public function getRevenueByTbbmData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('tbbms', 'transaksi_penjualan.id_tbbm', '=', 'tbbms.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->select([
                'tbbms.nama as tbbm_name',
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as revenue'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.10) as estimated_profit'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            ])
            ->groupBy('tbbms.id', 'tbbms.nama')
            ->orderBy('revenue', 'desc')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('refresh-charts');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('refresh-charts');
    }
}
