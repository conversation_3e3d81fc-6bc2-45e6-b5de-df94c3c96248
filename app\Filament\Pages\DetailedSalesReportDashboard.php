<?php

namespace App\Filament\Pages;

use App\Models\Pelanggan;
use App\Models\Tbbm;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;

class DetailedSalesReportDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';
    protected static ?string $navigationLabel = 'Laporan Penjualan Detail';
    protected static ?string $title = 'Dashboard Laporan Penjualan Detail (Neto vs Bruto)';
    protected static string $view = 'filament.pages.detailed-sales-report-dashboard';
    protected static ?int $navigationSort = 6;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]


    public ?string $selectedPeriod = null;
    #[Url(keep: true)]

    public ?string $selectedTipe = null;
    #[Url(keep: true)]

    public ?string $selectedTbbm = null;
    #[Url(keep: true)]

    public ?string $selectedCustomer = null;
    public $startDate = null;
    public $endDate = null;

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_year';
        $this->startDate = $this->startDate ?? now()->startOfYear()->format('Y-m-d');
        $this->endDate = $this->endDate ?? now()->endOfYear()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'current_month' => 'Bulan Ini',
                        'last_month' => 'Bulan Lalu',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                        'custom' => 'Custom Range',
                    ])
                    ->default('current_year')
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        $this->updateDateRange($state);
                    }),

                DatePicker::make('startDate')
                    ->label('Tanggal Mulai')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                DatePicker::make('endDate')
                    ->label('Tanggal Akhir')
                    ->visible(fn() => $this->selectedPeriod === 'custom')
                    ->live(),

                Select::make('selectedTipe')
                    ->label('Tipe Penjualan (Opsional)')
                    ->options([
                        'dagang' => 'Dagang',
                        'jasa' => 'Jasa',
                    ])
                    ->placeholder('Semua Tipe')
                    ->live(),

                Select::make('selectedTbbm')
                    ->label('TBBM (Opsional)')
                    ->options(Tbbm::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua TBBM')
                    ->live(),

                Select::make('selectedCustomer')
                    ->label('Pelanggan (Opsional)')
                    ->options(Pelanggan::pluck('nama', 'id'))
                    ->searchable()
                    ->placeholder('Semua Pelanggan')
                    ->live(),
            ])
            ->columns(6);
    }

    public function updateDateRange($period): void
    {
        $now = Carbon::now();

        match ($period) {
            'current_month' => [
                $this->startDate = $now->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'last_month' => [
                $this->startDate = $now->subMonth()->startOfMonth()->format('Y-m-d'),
                $this->endDate = $now->endOfMonth()->format('Y-m-d')
            ],
            'current_quarter' => [
                $this->startDate = $now->startOfQuarter()->format('Y-m-d'),
                $this->endDate = $now->endOfQuarter()->format('Y-m-d')
            ],
            'current_year' => [
                $this->startDate = $now->startOfYear()->format('Y-m-d'),
                $this->endDate = $now->endOfYear()->format('Y-m-d')
            ],
            default => null
        };
    }

    public function getDateRange(): array
    {
        return [
            Carbon::parse($this->startDate),
            Carbon::parse($this->endDate)
        ];
    }

    public function getDetailedSalesKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $baseQuery = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedTipe) {
            $baseQuery->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $baseQuery->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $baseQuery->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        // Gross Revenue (Bruto) - Before any deductions
        $grossRevenue = $baseQuery->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        // Calculate estimated costs and deductions
        // Assuming 10% tax, 5% operational cost, 2% admin fee (adjust based on your business logic)
        $taxRate = 0.10;
        $operationalCostRate = 0.05;
        $adminFeeRate = 0.02;

        $taxAmount = $grossRevenue * $taxRate;
        $operationalCost = $grossRevenue * $operationalCostRate;
        $adminFee = $grossRevenue * $adminFeeRate;

        // Net Revenue (Neto) - After deductions
        $netRevenue = $grossRevenue - $taxAmount - $operationalCost - $adminFee;

        // Volume metrics
        $totalVolume = $baseQuery->sum('penjualan_detail.volume_item');
        $avgPricePerUnit = $totalVolume > 0 ? $grossRevenue / $totalVolume : 0;

        // Delivery costs (estimated based on delivery orders)
        $deliveryCosts = DB::table('delivery_order')
            ->join('transaksi_penjualan', 'delivery_order.id_transaksi', '=', 'transaksi_penjualan.id')
            ->whereBetween('delivery_order.tanggal_delivery', [$startDate, $endDate])
            ->when($this->selectedTipe, fn($q) => $q->where('transaksi_penjualan.tipe', $this->selectedTipe))
            ->when($this->selectedTbbm, fn($q) => $q->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm))
            ->when($this->selectedCustomer, fn($q) => $q->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer))
            ->sum('delivery_order.biaya_sewa_jasa') ?? 0;

        // Profit margin calculation
        $totalCosts = $taxAmount + $operationalCost + $adminFee + $deliveryCosts;
        $profit = $grossRevenue - $totalCosts;
        $profitMargin = $grossRevenue > 0 ? round(($profit / $grossRevenue) * 100, 2) : 0;

        return [
            'gross_revenue' => $grossRevenue,
            'net_revenue' => $netRevenue,
            'tax_amount' => $taxAmount,
            'operational_cost' => $operationalCost,
            'admin_fee' => $adminFee,
            'delivery_costs' => $deliveryCosts,
            'total_costs' => $totalCosts,
            'profit' => $profit,
            'profit_margin' => $profitMargin,
            'total_volume' => $totalVolume,
            'avg_price_per_unit' => $avgPricePerUnit,
        ];
    }

    public function getRevenueBreakdownData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->join('pelanggan', 'transaksi_penjualan.id_pelanggan', '=', 'pelanggan.id')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedTipe) {
            $query->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        return $query->select([
            'pelanggan.nama as customer_name',
            'transaksi_penjualan.tipe as sales_type',
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
            DB::raw('SUM(penjualan_detail.volume_item) as total_volume'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as gross_revenue'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.9) as net_revenue'), // Assuming 10% deduction
            DB::raw('AVG(penjualan_detail.harga_jual) as avg_unit_price'),
        ])
            ->groupBy('pelanggan.id', 'pelanggan.nama', 'transaksi_penjualan.tipe')
            ->orderBy('gross_revenue', 'desc')
            ->limit(20)
            ->get()
            ->toArray();
    }

    public function getMonthlyTrendData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $query = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate]);

        // Apply filters
        if ($this->selectedTipe) {
            $query->where('transaksi_penjualan.tipe', $this->selectedTipe);
        }

        if ($this->selectedTbbm) {
            $query->where('transaksi_penjualan.id_tbbm', $this->selectedTbbm);
        }

        if ($this->selectedCustomer) {
            $query->where('transaksi_penjualan.id_pelanggan', $this->selectedCustomer);
        }

        return $query->select([
            DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m") as month'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as gross_revenue'),
            DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual * 0.9) as net_revenue'),
            DB::raw('SUM(penjualan_detail.volume_item) as total_volume'),
            DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
        ])
            ->groupBy(DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedStartDate(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedEndDate(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedSelectedTipe(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedSelectedTbbm(): void
    {
        $this->dispatch('$refresh');
    }

    public function updatedSelectedCustomer(): void
    {
        $this->dispatch('$refresh');
    }
}
