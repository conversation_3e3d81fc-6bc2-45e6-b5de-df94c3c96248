<?php

namespace App\Filament\Pages;

use App\Models\TransaksiPenjualan;
use App\Models\DeliveryOrder;
use App\Models\Pelanggan;
use App\Models\Kendaraan;
use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;

class ExecutiveSummaryDashboard extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar-square';
    protected static ?string $navigationLabel = 'Executive Summary';
    protected static ?string $title = 'Executive Summary Dashboard';
    protected static string $view = 'filament.pages.executive-summary-dashboard';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationGroup = 'Dashboard';

    #[Url(keep: true)]
    public ?string $selectedPeriod = null;

    public static function canAccess(): bool
    {
        return true; // Adjust based on your permission system
    }

    public function mount(): void
    {
        $this->selectedPeriod = $this->selectedPeriod ?? 'current_year';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedPeriod')
                    ->label('Periode')
                    ->options([
                        'today' => 'Hari Ini',
                        'current_week' => 'Minggu Ini',
                        'current_month' => 'Bulan Ini',
                        'current_quarter' => 'Kuartal Ini',
                        'current_year' => 'Tahun Ini',
                    ])
                    ->default('current_year')
                    ->live(),
            ])
            ->columns(1);
    }

    public function getDateRange(): array
    {
        $now = Carbon::now();

        return match ($this->selectedPeriod) {
            'today' => [$now->startOfDay(), $now->endOfDay()],
            'current_week' => [$now->startOfWeek(), $now->endOfWeek()],
            'current_month' => [$now->startOfMonth(), $now->endOfMonth()],
            'current_quarter' => [$now->startOfQuarter(), $now->endOfQuarter()],
            'current_year' => [$now->startOfYear(), $now->endOfYear()],
            default => [$now->startOfMonth(), $now->endOfMonth()],
        };
    }

    public function getExecutiveKpiData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        // Revenue Metrics
        $totalRevenue = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        // Previous period for comparison
        $previousPeriod = $this->getPreviousPeriodRange();
        $previousRevenue = DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', $previousPeriod)
            ->sum(DB::raw('penjualan_detail.volume_item * penjualan_detail.harga_jual'));

        $revenueGrowth = $previousRevenue > 0 ? round((($totalRevenue - $previousRevenue) / $previousRevenue) * 100, 1) : 0;

        // Sales Orders
        $totalSO = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])->count();
        $previousSO = TransaksiPenjualan::whereBetween('tanggal', $previousPeriod)->count();
        $soGrowth = $previousSO > 0 ? round((($totalSO - $previousSO) / $previousSO) * 100, 1) : 0;

        // Deliveries
        $totalDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])->count();
        $completedDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')->count();
        $deliveryRate = $totalDeliveries > 0 ? round(($completedDeliveries / $totalDeliveries) * 100, 1) : 0;

        // Customer Metrics
        $activeCustomers = TransaksiPenjualan::whereBetween('tanggal', [$startDate, $endDate])
            ->distinct('id_pelanggan')->count();
        $totalCustomers = Pelanggan::count();

        // Operational Metrics
        $activeVehicles = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->distinct('id_kendaraan')->count();
        $totalVehicles = Kendaraan::count();
        $vehicleUtilization = $totalVehicles > 0 ? round(($activeVehicles / $totalVehicles) * 100, 1) : 0;

        // Average Order Value
        $avgOrderValue = $totalSO > 0 ? $totalRevenue / $totalSO : 0;

        return [
            'total_revenue' => $totalRevenue,
            'revenue_growth' => $revenueGrowth,
            'total_so' => $totalSO,
            'so_growth' => $soGrowth,
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'delivery_rate' => $deliveryRate,
            'active_customers' => $activeCustomers,
            'total_customers' => $totalCustomers,
            'active_vehicles' => $activeVehicles,
            'total_vehicles' => $totalVehicles,
            'vehicle_utilization' => $vehicleUtilization,
            'avg_order_value' => $avgOrderValue,
        ];
    }

    public function getPreviousPeriodRange(): array
    {
        $now = Carbon::now();

        return match ($this->selectedPeriod) {
            'today' => [$now->subDay()->startOfDay(), $now->subDay()->endOfDay()],
            'current_week' => [$now->subWeek()->startOfWeek(), $now->subWeek()->endOfWeek()],
            'current_month' => [$now->subMonth()->startOfMonth(), $now->subMonth()->endOfMonth()],
            'current_quarter' => [$now->subQuarter()->startOfQuarter(), $now->subQuarter()->endOfQuarter()],
            'current_year' => [$now->subYear()->startOfYear(), $now->subYear()->endOfYear()],
            default => [$now->subMonth()->startOfMonth(), $now->subMonth()->endOfMonth()],
        };
    }

    public function getRevenueByTypeData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->select([
                'transaksi_penjualan.tipe',
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            ])
            ->groupBy('transaksi_penjualan.tipe')
            ->get()
            ->toArray();
    }

    public function getMonthlyTrendData(): array
    {
        $endDate = Carbon::now();
        $startDate = $endDate->copy()->subMonths(11)->startOfMonth();

        return DB::table('transaksi_penjualan')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->select([
                DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m") as month'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
            ])
            ->groupBy(DB::raw('DATE_FORMAT(transaksi_penjualan.tanggal, "%Y-%m")'))
            ->orderBy('month')
            ->get()
            ->toArray();
    }

    public function getTopPerformingTbbmData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        return DB::table('transaksi_penjualan')
            ->join('tbbms', 'transaksi_penjualan.id_tbbm', '=', 'tbbms.id')
            ->join('penjualan_detail', 'transaksi_penjualan.id', '=', 'penjualan_detail.id_transaksi_penjualan')
            ->whereBetween('transaksi_penjualan.tanggal', [$startDate, $endDate])
            ->select([
                'tbbms.nama as tbbm_name',
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id) as total_orders'),
                DB::raw('SUM(penjualan_detail.volume_item * penjualan_detail.harga_jual) as total_revenue'),
                DB::raw('COUNT(DISTINCT transaksi_penjualan.id_pelanggan) as unique_customers'),
            ])
            ->groupBy('tbbms.id', 'tbbms.nama')
            ->orderBy('total_revenue', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    public function getOperationalSummaryData(): array
    {
        [$startDate, $endDate] = $this->getDateRange();

        $onTimeDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')
            ->whereRaw('DATE(waktu_selesai_muat) <= DATE(tanggal_delivery)')
            ->count();

        $totalCompletedDeliveries = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')
            ->count();

        $onTimeRate = $totalCompletedDeliveries > 0 ? round(($onTimeDeliveries / $totalCompletedDeliveries) * 100, 1) : 0;

        $avgDeliveryTime = DeliveryOrder::whereBetween('tanggal_delivery', [$startDate, $endDate])
            ->where('status_muat', 'selesai')
            ->whereNotNull('waktu_muat')
            ->whereNotNull('waktu_selesai_muat')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, waktu_muat, waktu_selesai_muat)) as avg_time')
            ->value('avg_time') ?? 0;

        return [
            'on_time_deliveries' => $onTimeDeliveries,
            'total_completed_deliveries' => $totalCompletedDeliveries,
            'on_time_rate' => $onTimeRate,
            'avg_delivery_time' => round($avgDeliveryTime, 1),
        ];
    }

    // Method to refresh data when filters change
    public function updatedSelectedPeriod(): void
    {
        // Force re-render of the component
        $this->dispatch('$refresh');
    }
}
