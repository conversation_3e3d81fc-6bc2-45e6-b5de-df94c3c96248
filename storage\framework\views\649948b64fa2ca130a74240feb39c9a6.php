<?php
    $attributes = $record->attributes ?? [];
    $category = $record->category;
?>

<div class="space-y-6">
    <?php if(!empty($attributes)): ?>
        <?php switch($category):
            case ('ganti_oli'): ?>
                <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-3">
                        🛢️ Detail Ganti Oli Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'jenis_oli' => 'Jenis Oli',
                            'merk_oli' => 'Merk Oli',
                            'volume_oli' => 'Volume Oli (Liter)',
                            'kilometer' => 'Kilometer Saat Ganti',
                            'kilometer_berikutnya' => 'Kilometer Ganti Berikutnya',
                            'bengkel' => '<PERSON><PERSON>',
                            'alamat_bengkel' => '<PERSON><PERSON><PERSON>',
                            'teknisi' => '<PERSON>a Teknisi',
                            'filter_oli' => 'Ganti Filter Oli',
                            'filter_udara' => 'Ganti Filter Udara',
                            'catatan' => 'Catatan Tambahan'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if($key === 'filter_oli' || $key === 'filter_udara'): ?>
                                            <?php echo e($attributes[$key] ? 'Ya' : 'Tidak'); ?>

                                        <?php elseif($key === 'kilometer' || $key === 'kilometer_berikutnya'): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> km
                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php case ('vehicle_maintenance'): ?>
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
                        🔧 Detail Perawatan Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'jenis_perawatan' => 'Jenis Perawatan',
                            'suku_cadang' => 'Suku Cadang',
                            'merk_suku_cadang' => 'Merk Suku Cadang',
                            'jumlah_suku_cadang' => 'Jumlah',
                            'bengkel' => 'Nama Bengkel',
                            'alamat_bengkel' => 'Alamat Bengkel',
                            'teknisi' => 'Nama Teknisi',
                            'kilometer' => 'Kilometer Saat Service',
                            'estimasi_selesai' => 'Estimasi Selesai',
                            'garansi' => 'Garansi',
                            'catatan' => 'Catatan Tambahan'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if($key === 'kilometer'): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> km
                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php case ('vehicle_fuel'): ?>
                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">
                        ⛽ Detail Bahan Bakar Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'jenis_bbm' => 'Jenis BBM',
                            'liter' => 'Jumlah Liter',
                            'harga_per_liter' => 'Harga per Liter',
                            'spbu' => 'Nama SPBU',
                            'alamat_spbu' => 'Alamat SPBU',
                            'kilometer' => 'Kilometer Saat Isi',
                            'tangki_penuh' => 'Tangki Penuh',
                            'operator' => 'Operator SPBU',
                            'no_struk' => 'Nomor Struk',
                            'catatan' => 'Catatan Tambahan'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if($key === 'liter'): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> L
                                        <?php elseif($key === 'harga_per_liter'): ?>
                                            Rp <?php echo e(number_format($attributes[$key])); ?>

                                        <?php elseif($key === 'kilometer'): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> km
                                        <?php elseif($key === 'tangki_penuh'): ?>
                                            <?php echo e($attributes[$key] ? 'Ya' : 'Tidak'); ?>

                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php case ('vehicle_tax'): ?>
                <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">
                        📋 Detail Pajak dan STNK Kendaraan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'jenis_pajak' => 'Jenis Pajak',
                            'masa_berlaku' => 'Masa Berlaku',
                            'samsat' => 'Kantor Samsat',
                            'alamat_samsat' => 'Alamat Samsat',
                            'petugas' => 'Nama Petugas',
                            'no_kuitansi' => 'Nomor Kuitansi',
                            'denda' => 'Denda (jika ada)',
                            'catatan' => 'Catatan Tambahan'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if($key === 'denda'): ?>
                                            Rp <?php echo e(number_format($attributes[$key])); ?>

                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php case ('delivery_reimbursement'): ?>
                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">
                        🚚 Detail Reimburs Delivery Order
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'odometer_awal' => 'Odometer Awal',
                            'odometer_akhir' => 'Odometer Akhir',
                            'jarak_tempuh' => 'Jarak Tempuh (km)',
                            'tujuan_delivery' => 'Tujuan Delivery',
                            'no_delivery_order' => 'No. Delivery Order',
                            'nama_customer' => 'Nama Customer',
                            'jenis_produk' => 'Jenis Produk',
                            'biaya_tol' => 'Biaya Tol',
                            'biaya_parkir' => 'Biaya Parkir',
                            'catatan_perjalanan' => 'Catatan Perjalanan'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if(in_array($key, ['odometer_awal', 'odometer_akhir'])): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> km
                                        <?php elseif($key === 'jarak_tempuh'): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> km
                                        <?php elseif(in_array($key, ['biaya_tol', 'biaya_parkir'])): ?>
                                            Rp <?php echo e(number_format($attributes[$key])); ?>

                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php case ('uang_jalan_sc'): ?>
                <div class="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-indigo-800 dark:text-indigo-200 mb-3">
                        💰 Detail Uang Jalan SC
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'nama_supir' => 'Nama Supir',
                            'nama_crew' => 'Nama Crew',
                            'no_kendaraan' => 'No. Kendaraan',
                            'rute_perjalanan' => 'Rute Perjalanan',
                            'tujuan' => 'Tujuan',
                            'tanggal_berangkat' => 'Tanggal Berangkat',
                            'tanggal_kembali' => 'Tanggal Kembali',
                            'jarak_tempuh' => 'Jarak Tempuh (km)',
                            'uang_makan' => 'Uang Makan',
                            'uang_inap' => 'Uang Inap',
                            'uang_bensin' => 'Uang Bensin',
                            'uang_tol' => 'Uang Tol',
                            'uang_parkir' => 'Uang Parkir',
                            'lain_lain' => 'Lain-lain',
                            'total_uang_jalan' => 'Total Uang Jalan',
                            'catatan' => 'Catatan'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if($key === 'jarak_tempuh'): ?>
                                            <?php echo e(number_format($attributes[$key])); ?> km
                                        <?php elseif(in_array($key, ['uang_makan', 'uang_inap', 'uang_bensin', 'uang_tol', 'uang_parkir', 'lain_lain', 'total_uang_jalan'])): ?>
                                            Rp <?php echo e(number_format($attributes[$key])); ?>

                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php case ('commission_fee'): ?>
                <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-orange-800 dark:text-orange-200 mb-3">
                        💼 Detail Biaya Komisi
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = [
                            'nama_penerima_komisi' => 'Nama Penerima',
                            'npwp_penerima' => 'NPWP Penerima',
                            'jenis_komisi' => 'Jenis Komisi',
                            'dasar_perhitungan' => 'Dasar Perhitungan',
                            'persentase_komisi' => 'Persentase Komisi (%)',
                            'nilai_transaksi' => 'Nilai Transaksi',
                            'komisi_bruto' => 'Komisi Bruto',
                            'pph_komisi' => 'PPh Komisi',
                            'komisi_netto' => 'Komisi Netto',
                            'periode_komisi' => 'Periode Komisi',
                            'bukti_transaksi' => 'Bukti Transaksi',
                            'catatan_pajak' => 'Catatan Pajak'
                        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(isset($attributes[$key]) && !empty($attributes[$key])): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e($label); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php if(in_array($key, ['nilai_transaksi', 'komisi_bruto', 'pph_komisi', 'komisi_netto'])): ?>
                                            Rp <?php echo e(number_format($attributes[$key])); ?>

                                        <?php elseif($key === 'persentase_komisi'): ?>
                                            <?php echo e($attributes[$key]); ?>%
                                        <?php else: ?>
                                            <?php echo e($attributes[$key]); ?>

                                        <?php endif; ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php break; ?>

            <?php default: ?>
                <div class="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                        📄 Data Tambahan
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!empty($value)): ?>
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400"><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?></span>
                                    <span class="text-gray-900 dark:text-gray-100">
                                        <?php echo e(is_array($value) ? implode(', ', $value) : $value); ?>

                                    </span>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
        <?php endswitch; ?>
    <?php else: ?>
        <div class="text-center py-8">
            <div class="text-gray-400 dark:text-gray-500 text-lg">
                📄 Tidak ada data tambahan untuk expense request ini
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\laragon\www\lrp\resources\views\modals\expense-attributes-detail.blade.php ENDPATH**/ ?>