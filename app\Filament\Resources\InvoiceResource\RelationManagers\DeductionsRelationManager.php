<?php

namespace App\Filament\Resources\InvoiceResource\RelationManagers;

use App\Models\InvoiceDeduction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;

class DeductionsRelationManager extends RelationManager
{
    protected static string $relationship = 'deductions';

    protected static ?string $title = 'Riwayat Potongan';

    protected static ?string $modelLabel = 'Potongan';

    protected static ?string $pluralModelLabel = 'Potongan';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Potongan')
                    ->schema([
                        Forms\Components\Select::make('deduction_type')
                            ->label('Tipe Potongan')
                            ->options(InvoiceDeduction::getTypeOptions())
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // Set default percentage for certain types
                                if ($state === 'ppn') {
                                    $set('percentage', 11);
                                } elseif ($state === 'pph') {
                                    $set('percentage', 2);
                                }
                            }),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('amount')
                                    ->label('Jumlah Potongan')
                                    ->numeric()
                                    ->prefix('IDR')
                                    ->required()
                                    ->step(0.01),

                                Forms\Components\TextInput::make('percentage')
                                    ->label('Persentase (%)')
                                    ->numeric()
                                    ->suffix('%')
                                    ->step(0.01)
                                    ->helperText('Opsional - untuk referensi'),
                            ]),

                        Forms\Components\DateTimePicker::make('deduction_date')
                            ->label('Tanggal Potongan')
                            ->required()
                            ->default(now()),

                        Forms\Components\TextInput::make('reference_number')
                            ->label('Nomor Referensi')
                            ->helperText('Nomor dokumen atau referensi terkait'),

                        Forms\Components\TextInput::make('description')
                            ->label('Deskripsi')
                            ->helperText('Deskripsi singkat potongan'),

                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->rows(3)
                            ->helperText('Catatan tambahan tentang potongan'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('deduction_type')
            ->columns([
                Tables\Columns\BadgeColumn::make('deduction_type')
                    ->label('Tipe')
                    ->formatStateUsing(
                        fn(string $state): string =>
                        InvoiceDeduction::getTypeLabels()[$state] ?? $state
                    )
                    ->colors([
                        'primary' => 'ppn',
                        'warning' => 'pph',
                        'danger' => 'losis',
                        'success' => 'admin_bank',
                    ]),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Jumlah')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('percentage')
                    ->label('Persentase')
                    ->formatStateUsing(
                        fn(?float $state): string =>
                        $state ? $state . '%' : '-'
                    ),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('deduction_date')
                    ->label('Tanggal')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Ref. Number')
                    ->limit(20)
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('journal.journal_number')
                    ->label('No. Jurnal')
                    ->placeholder('-')
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('journal.status')
                    ->label('Status Jurnal')
                    ->colors([
                        'success' => 'Posted',
                        'warning' => 'Draft',
                        'danger' => 'Error',
                    ])
                    ->placeholder('-')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Dibuat Oleh')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('deduction_type')
                    ->label('Tipe Potongan')
                    ->options(InvoiceDeduction::getTypeOptions()),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['created_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function ($record) {
                        $this->updateInvoiceTotals();

                        // Auto-post to journal if invoice is published
                        if ($record->invoice->is_published) {
                            $journal = $record->createJournalEntry();
                            if ($journal) {
                                Notification::make()
                                    ->title('Potongan berhasil ditambahkan dan diposting ke jurnal')
                                    ->body("Jurnal #{$journal->journal_number} berhasil dibuat")
                                    ->success()
                                    ->send();
                            } else {
                                Notification::make()
                                    ->title('Potongan berhasil ditambahkan')
                                    ->body('Namun gagal membuat jurnal. Silakan cek log untuk detail.')
                                    ->warning()
                                    ->send();
                            }
                        } else {
                            Notification::make()
                                ->title('Potongan berhasil ditambahkan')
                                ->body('Jurnal akan dibuat otomatis ketika invoice diterbitkan.')
                                ->success()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['updated_by'] = auth()->id();
                        return $data;
                    })
                    ->after(function () {
                        $this->updateInvoiceTotals();
                    }),

                Tables\Actions\Action::make('post_journal')
                    ->label('Post Jurnal')
                    ->icon('heroicon-o-document-text')
                    ->color('info')
                    ->visible(
                        fn($record) =>
                        !$record->journal_id &&
                            $record->invoice->is_published
                    )
                    ->requiresConfirmation()
                    ->modalHeading('Post ke Jurnal')
                    ->modalDescription('Potongan ini akan diposting ke jurnal.')
                    ->action(function ($record) {
                        $journal = $record->createJournalEntry();
                        if ($journal) {
                            Notification::make()
                                ->title('Jurnal berhasil dibuat')
                                ->body("Jurnal #{$journal->journal_number} berhasil dibuat")
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Gagal membuat jurnal')
                                ->body('Silakan cek log untuk detail error.')
                                ->danger()
                                ->send();
                        }
                    }),

                Tables\Actions\DeleteAction::make()
                    ->after(function () {
                        $this->updateInvoiceTotals();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->after(function () {
                            $this->updateInvoiceTotals();
                        }),
                ]),
            ])
            ->defaultSort('deduction_date', 'desc');
    }

    /**
     * Update invoice totals after deduction changes
     */
    protected function updateInvoiceTotals(): void
    {
        $invoice = $this->ownerRecord;

        // Recalculate totals
        $totalPaid = $invoice->payments()->accepted()->sum('amount');
        $totalDeductions = $invoice->deductions()->sum('amount');
        $netAmount = $invoice->total_invoice - $totalDeductions;
        $remainingAmount = $netAmount - $totalPaid;

        // Update invoice with new calculations
        $invoice->update([
            'total_terbayar' => $totalPaid,
            'sisa_tagihan' => $remainingAmount,
        ]);
    }
}
