<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <?php echo e($this->form); ?>


            <div class="mt-6 flex justify-end">
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']); ?>
                    Export PDF
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Balance Sheet Report -->
        <!--[if BLOCK]><![endif]--><?php if($this->report_date): ?>
            <?php
                $data = $this->getBalanceSheetData();
            ?>

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        NERACA
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        Per <?php echo e(\Carbon\Carbon::parse($this->report_date)->format('d F Y')); ?>

                    </p>
                </div>

                <!-- Balance Status -->
                <!--[if BLOCK]><![endif]--><?php if($data['is_balanced']): ?>
                    <div class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-green-800 dark:text-green-200 font-medium">
                                Neraca Seimbang: Aset = Kewajiban + Ekuitas
                            </span>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-red-800 dark:text-red-200 font-medium">
                                Neraca Tidak Seimbang! Periksa kembali jurnal Anda.
                            </span>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- ASET -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                            ASET
                        </h3>

                        <!--[if BLOCK]><![endif]--><?php if(count($data['assets']) > 0): ?>
                            <div class="space-y-2">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['assets']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $asset): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center py-1">
                                        <span class="text-gray-700 dark:text-gray-300">
                                            <?php echo e($asset['account']->kode_akun); ?> - <?php echo e($asset['account']->nama_akun); ?>

                                        </span>
                                        <span class="font-medium text-gray-900 dark:text-gray-100">
                                            Rp <?php echo e(number_format($asset['balance'], 0, ',', '.')); ?>

                                        </span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                <div class="flex justify-between items-center py-3 border-t-2 border-gray-300 dark:border-gray-600 font-bold text-lg">
                                    <span class="text-gray-900 dark:text-gray-100">TOTAL ASET</span>
                                    <span class="text-blue-600 dark:text-blue-400">
                                        Rp <?php echo e(number_format($data['total_assets'], 0, ',', '.')); ?>

                                    </span>
                                </div>
                            </div>
                        <?php else: ?>
                            <p class="text-gray-500 dark:text-gray-400 italic">Tidak ada data aset</p>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- KEWAJIBAN & EKUITAS -->
                    <div>
                        <!-- KEWAJIBAN -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                                KEWAJIBAN
                            </h3>

                            <!--[if BLOCK]><![endif]--><?php if(count($data['liabilities']) > 0): ?>
                                <div class="space-y-2">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['liabilities']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $liability): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="flex justify-between items-center py-1">
                                            <span class="text-gray-700 dark:text-gray-300">
                                                <?php echo e($liability['account']->kode_akun); ?> - <?php echo e($liability['account']->nama_akun); ?>

                                            </span>
                                            <span class="font-medium text-gray-900 dark:text-gray-100">
                                                Rp <?php echo e(number_format($liability['balance'], 0, ',', '.')); ?>

                                            </span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                    <div class="flex justify-between items-center py-2 border-t border-gray-200 dark:border-gray-700 font-semibold">
                                        <span class="text-gray-900 dark:text-gray-100">Total Kewajiban</span>
                                        <span class="text-red-600 dark:text-red-400">
                                            Rp <?php echo e(number_format($data['total_liabilities'], 0, ',', '.')); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 dark:text-gray-400 italic">Tidak ada data kewajiban</p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- EKUITAS -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                                EKUITAS
                            </h3>

                            <!--[if BLOCK]><![endif]--><?php if(count($data['equity']) > 0): ?>
                                <div class="space-y-2">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['equity']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $equity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="flex justify-between items-center py-1">
                                            <span class="text-gray-700 dark:text-gray-300">
                                                <?php echo e($equity['account']->kode_akun); ?> - <?php echo e($equity['account']->nama_akun); ?>

                                            </span>
                                            <span class="font-medium text-gray-900 dark:text-gray-100">
                                                Rp <?php echo e(number_format($equity['balance'], 0, ',', '.')); ?>

                                            </span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                    <div class="flex justify-between items-center py-2 border-t border-gray-200 dark:border-gray-700 font-semibold">
                                        <span class="text-gray-900 dark:text-gray-100">Total Ekuitas</span>
                                        <span class="text-green-600 dark:text-green-400">
                                            Rp <?php echo e(number_format($data['total_equity'], 0, ',', '.')); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 dark:text-gray-400 italic">Tidak ada data ekuitas</p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- TOTAL KEWAJIBAN + EKUITAS -->
                        <div class="mt-6 pt-4 border-t-2 border-gray-300 dark:border-gray-600">
                            <div class="flex justify-between items-center py-3 bg-gray-50 dark:bg-gray-700 px-4 rounded-lg font-bold text-lg">
                                <span class="text-gray-900 dark:text-gray-100">
                                    TOTAL KEWAJIBAN + EKUITAS
                                </span>
                                <span class="text-blue-600 dark:text-blue-400">
                                    Rp <?php echo e(number_format($data['total_liabilities_equity'], 0, ',', '.')); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center py-8">
                    <div class="text-gray-400 dark:text-gray-500 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                        Pilih Tanggal Laporan
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        Silakan pilih tanggal untuk melihat laporan neraca
                    </p>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\lrp\resources\views/filament/pages/balance-sheet.blade.php ENDPATH**/ ?>