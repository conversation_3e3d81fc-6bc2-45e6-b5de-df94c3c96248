<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use App\Services\NeracaService;
use Illuminate\Support\Facades\Auth;

class BalanceSheet extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static ?string $navigationLabel = 'Neraca';

    protected static ?string $title = 'Laporan Neraca';

    protected static ?string $navigationGroup = 'Manajemen Keuangan';

    protected static ?int $navigationSort = 32;

    protected static string $view = 'filament.pages.balance-sheet';

    public ?array $data = [];
    public $report_date = null;

    public static function canAccess(): bool
    {
        return Auth::user()?->can('page_BalanceSheet') ?? false;
    }

    public function mount(): void
    {
        $this->form->fill([
            'report_date' => now(),
        ]);

        $this->report_date = now()->format('Y-m-d');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Tanggal Laporan')
                    ->schema([
                        Forms\Components\DatePicker::make('report_date')
                            ->label('Tanggal Laporan')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->report_date = $state;
                            }),
                    ])
                    ->columns(1),
            ])
            ->statePath('data');
    }

    public function getBalanceSheetData(): array
    {
        if (!$this->report_date) {
            return [
                'aktiva' => [
                    'lancar' => ['items' => [], 'total' => 0],
                    'tetap' => ['items' => [], 'total' => 0],
                    'total_lancar' => 0,
                    'total_tetap' => 0,
                    'total' => 0
                ],
                'pasiva' => [
                    'hutang' => ['items' => [], 'total' => 0],
                    'modal' => ['items' => [], 'total' => 0],
                    'total_hutang' => 0,
                    'total_modal' => 0,
                    'total' => 0
                ],
                'is_balanced' => false,
                'periode' => [
                    'date' => '',
                    'title' => ''
                ]
            ];
        }

        $neracaService = new NeracaService();
        return $neracaService->generateReport($this->report_date);
    }

    /**
     * Format currency for display
     */
    public function formatCurrency($amount)
    {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}
