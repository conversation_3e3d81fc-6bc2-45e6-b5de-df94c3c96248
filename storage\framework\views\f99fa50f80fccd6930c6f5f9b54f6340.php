<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <?php echo e($this->form); ?>


            <div class="mt-6 flex justify-end">
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']); ?>
                    Export PDF
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Balance Sheet Report -->
        <?php if($this->start_date && $this->end_date): ?>
            <?php
                $data = $this->getBalanceSheetData();
            ?>

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        PT. LINTAS RIAU PRIMA
                    </h2>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-gray-100">
                        NERACA
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        <?php if($this->period_preset === 'custom'): ?>
                            PERIODE <?php echo e(\Carbon\Carbon::parse($this->start_date)->format('d F Y')); ?> s.d
                            <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('d F Y')); ?>

                        <?php else: ?>
                            <?php switch($this->period_preset):
                                case ('current_month'): ?>
                                    PERIODE BULAN <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('F Y')); ?>

                                <?php break; ?>

                                <?php case ('last_month'): ?>
                                    PERIODE BULAN <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('F Y')); ?>

                                <?php break; ?>

                                <?php case ('current_year'): ?>
                                    PERIODE TAHUN <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('Y')); ?>

                                <?php break; ?>

                                <?php case ('last_year'): ?>
                                    PERIODE TAHUN <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('Y')); ?>

                                <?php break; ?>

                                <?php default: ?>
                                    PERIODE <?php echo e(\Carbon\Carbon::parse($this->start_date)->format('d F Y')); ?> s.d
                                    <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('d F Y')); ?>

                            <?php endswitch; ?>
                        <?php endif; ?>
                    </p>
                </div>

                <!-- Balance Status -->
                <?php if($data['is_balanced']): ?>
                    <div
                        class="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="currentColor"
                                viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-green-800 dark:text-green-200 font-medium">
                                Neraca Seimbang: Aktiva = Pasiva
                            </span>
                        </div>
                    </div>
                <?php else: ?>
                    <div
                        class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" fill="currentColor"
                                viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-red-800 dark:text-red-200 font-medium">
                                Neraca Tidak Seimbang! Periksa kembali jurnal Anda.
                            </span>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- AKTIVA -->
                    <div>
                        <h3
                            class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                            AKTIVA
                        </h3>

                        <!-- Aktiva Lancar -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">
                                Aktiva Lancar
                            </h4>
                            <?php if(count($data['aktiva']['lancar']['items']) > 0): ?>
                                <div class="space-y-2 ml-4">
                                    <?php $__currentLoopData = $data['aktiva']['lancar']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-2">
                                            <div class="flex justify-between items-center py-1">
                                                <span
                                                    class="font-medium text-gray-700 dark:text-gray-300"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 ml-4">
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                                        <?php echo e($account['kode_akun']); ?> - <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-sm text-gray-800 dark:text-gray-200">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 ml-4">
                                    <div class="flex justify-between items-center font-semibold">
                                        <span class="text-gray-900 dark:text-gray-100">Total Aktiva Lancar</span>
                                        <span class="text-blue-600 dark:text-blue-400">
                                            <?php echo e($this->formatCurrency($data['aktiva']['total_lancar'])); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 dark:text-gray-400 italic ml-4">Tidak ada data aktiva lancar</p>
                            <?php endif; ?>
                        </div>

                        <!-- Aktiva Tetap -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">
                                Aktiva Tetap
                            </h4>
                            <?php if(count($data['aktiva']['tetap']['items']) > 0): ?>
                                <div class="space-y-2 ml-4">
                                    <?php $__currentLoopData = $data['aktiva']['tetap']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-2">
                                            <div class="flex justify-between items-center py-1">
                                                <span
                                                    class="font-medium text-gray-700 dark:text-gray-300"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 ml-4">
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                                        <?php echo e($account['kode_akun']); ?> - <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-sm text-gray-800 dark:text-gray-200">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 ml-4">
                                    <div class="flex justify-between items-center font-semibold">
                                        <span class="text-gray-900 dark:text-gray-100">Total Aktiva Tetap</span>
                                        <span class="text-blue-600 dark:text-blue-400">
                                            <?php echo e($this->formatCurrency($data['aktiva']['total_tetap'])); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 dark:text-gray-400 italic ml-4">Tidak ada data aktiva tetap</p>
                            <?php endif; ?>
                        </div>

                        <!-- Total Aktiva -->
                        <div class="mt-6 pt-4 border-t-2 border-gray-300 dark:border-gray-600">
                            <div
                                class="flex justify-between items-center py-3 bg-gray-50 dark:bg-gray-700 px-4 rounded-lg font-bold text-lg">
                                <span class="text-gray-900 dark:text-gray-100">TOTAL AKTIVA</span>
                                <span class="text-blue-600 dark:text-blue-400">
                                    <?php echo e($this->formatCurrency($data['aktiva']['total'])); ?>

                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- PASIVA -->
                    <div>
                        <h3
                            class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                            PASIVA
                        </h3>

                        <!-- Hutang -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">
                                Hutang
                            </h4>
                            <?php if(count($data['pasiva']['hutang']['items']) > 0): ?>
                                <div class="space-y-2 ml-4">
                                    <?php $__currentLoopData = $data['pasiva']['hutang']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-2">
                                            <div class="flex justify-between items-center py-1">
                                                <span
                                                    class="font-medium text-gray-700 dark:text-gray-300"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 ml-4">
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                                        <?php echo e($account['kode_akun']); ?> - <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-sm text-gray-800 dark:text-gray-200">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 ml-4">
                                    <div class="flex justify-between items-center font-semibold">
                                        <span class="text-gray-900 dark:text-gray-100">Total Hutang</span>
                                        <span class="text-red-600 dark:text-red-400">
                                            <?php echo e($this->formatCurrency($data['pasiva']['total_hutang'])); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 dark:text-gray-400 italic ml-4">Tidak ada data hutang</p>
                            <?php endif; ?>
                        </div>

                        <!-- Modal -->
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3">
                                Modal
                            </h4>
                            <?php if(count($data['pasiva']['modal']['items']) > 0): ?>
                                <div class="space-y-2 ml-4">
                                    <?php $__currentLoopData = $data['pasiva']['modal']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-2">
                                            <div class="flex justify-between items-center py-1">
                                                <span
                                                    class="font-medium text-gray-700 dark:text-gray-300"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 ml-4">
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                                        <?php echo e($account['kode_akun']); ?> - <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-sm text-gray-800 dark:text-gray-200">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 ml-4">
                                    <div class="flex justify-between items-center font-semibold">
                                        <span class="text-gray-900 dark:text-gray-100">Total Modal</span>
                                        <span class="text-green-600 dark:text-green-400">
                                            <?php echo e($this->formatCurrency($data['pasiva']['total_modal'])); ?>

                                        </span>
                                    </div>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 dark:text-gray-400 italic ml-4">Tidak ada data modal</p>
                            <?php endif; ?>
                        </div>

                        <!-- Total Pasiva -->
                        <div class="mt-6 pt-4 border-t-2 border-gray-300 dark:border-gray-600">
                            <div
                                class="flex justify-between items-center py-3 bg-gray-50 dark:bg-gray-700 px-4 rounded-lg font-bold text-lg">
                                <span class="text-gray-900 dark:text-gray-100">TOTAL PASIVA</span>
                                <span class="text-blue-600 dark:text-blue-400">
                                    <?php echo e($this->formatCurrency($data['pasiva']['total'])); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer with date -->
                <div class="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="text-right">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Pekanbaru, <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('d F Y')); ?>

                        </p>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center py-8">
                    <div class="text-gray-400 dark:text-gray-500 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                        Pilih Tanggal Laporan
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        Silakan pilih tanggal untuk melihat laporan neraca
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\lrp\resources\views\filament\pages\balance-sheet.blade.php ENDPATH**/ ?>