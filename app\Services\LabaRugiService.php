<?php

namespace App\Services;

use App\Models\LabaRugiMapping;
use App\Models\JournalEntry;
use App\Models\Akun;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;

class LabaRugiService
{
    /**
     * Generate Laba Rugi Report
     */
    public function generateReport($startDate, $endDate)
    {
        $startDate = Carbon::parse($startDate)->startOfDay();
        $endDate = Carbon::parse($endDate)->endOfDay();

        // Get all mappings grouped by category
        $mappings = LabaRugiMapping::getMappingsByCategory();

        $report = [
            'periode' => [
                'start' => $startDate->format('d/m/Y'),
                'end' => $endDate->format('d/m/Y'),
                'title' => 'PERIODE ' . $endDate->format('d F Y')
            ],
            'sections' => []
        ];

        // 1. PEREDARAN USAHA
        $report['sections']['peredaran_usaha'] = $this->buildSection(
            'PEREDARAN USAHA',
            ['Pendapatan Penjualan BBM'],
            $mappings,
            $startDate,
            $endDate
        );

        // 2. HPP
        $report['sections']['hpp'] = $this->buildSection(
            'HPP',
            ['HPP BBM', 'HPP Jasa Angkut', 'Persediaan'],
            $mappings,
            $startDate,
            $endDate
        );

        // Calculate Laba Bruto
        $peredaran_total = $report['sections']['peredaran_usaha']['total'];
        $hpp_total = $report['sections']['hpp']['total'];
        $laba_bruto = $peredaran_total - $hpp_total;

        $report['sections']['laba_bruto'] = [
            'title' => 'LABA BRUTO',
            'total' => $laba_bruto,
            'items' => []
        ];

        // 3. BEBAN UMUM / ADM / OPS
        $beban_categories = [
            'Gaji Karyawan',
            'Beban Utilitas',
            'ATK',
            'Ekspedisi',
            'Beban Perjalanan Dinas',
            'Beban pengurusan izin/lisensi',
            'BBM mobil operasional kantor',
            'Beban Lain -lain',
            'Beban Lain-lain',
            'Beban lain - lain',
            'Beban lain -lain'
        ];

        $report['sections']['beban_umum'] = $this->buildSection(
            'BEBAN UMUM / ADM / OPS',
            $beban_categories,
            $mappings,
            $startDate,
            $endDate,
            true // Group similar categories
        );

        // Calculate Laba Usaha
        $beban_umum_total = $report['sections']['beban_umum']['total'];
        $laba_usaha = $laba_bruto - $beban_umum_total;

        $report['sections']['laba_usaha'] = [
            'title' => 'LABA USAHA',
            'total' => $laba_usaha,
            'items' => []
        ];

        // 4. PENDAPATAN DAN BEBAN LUAR USAHA
        $report['sections']['pendapatan_luar_usaha'] = $this->buildSection(
            'PENDAPATAN DAN BEBAN LUAR USAHA',
            ['Pendapatan Jasa Giro', 'Pendapatan Bunga Bank', 'Pendapatan Lain Lain'],
            $mappings,
            $startDate,
            $endDate
        );

        $report['sections']['beban_luar_usaha'] = $this->buildSection(
            'BEBAN LUAR USAHA',
            ['Adm Bank', 'Pajak Bank', 'Bunga Bank', 'Lain Lain'],
            $mappings,
            $startDate,
            $endDate
        );

        // Calculate Laba Luar Usaha
        $pendapatan_luar_total = $report['sections']['pendapatan_luar_usaha']['total'];
        $beban_luar_total = $report['sections']['beban_luar_usaha']['total'];
        $laba_luar_usaha = $pendapatan_luar_total - $beban_luar_total;

        $report['sections']['laba_luar_usaha'] = [
            'title' => 'LABA LUAR USAHA',
            'total' => $laba_luar_usaha,
            'items' => []
        ];

        // Calculate Laba Bersih Sebelum Pajak
        $laba_bersih_sebelum_pajak = $laba_usaha + $laba_luar_usaha;

        $report['sections']['laba_bersih_sebelum_pajak'] = [
            'title' => 'LABA BERSIH SEBELUM PAJAK',
            'total' => $laba_bersih_sebelum_pajak,
            'items' => []
        ];

        // 5. PAJAK
        $pajak_categories = [
            'PBB',
            'PPN',
            'Pajak Pasal 21',
            'Pajak Pasal 22',
            'Pajak Pasal 23',
            'Pajak Pasal 25',
            'Pajak Pasal 29',
            'Pajak Lainnnya'
        ];

        $report['sections']['pajak'] = $this->buildSection(
            'Pajak Penghasilan',
            $pajak_categories,
            $mappings,
            $startDate,
            $endDate
        );

        // Calculate Laba Bersih Setelah Pajak
        $pajak_total = $report['sections']['pajak']['total'];
        $laba_bersih_setelah_pajak = $laba_bersih_sebelum_pajak - $pajak_total;

        $report['sections']['laba_bersih_setelah_pajak'] = [
            'title' => 'LABA BERSIH SETELAH PAJAK',
            'total' => $laba_bersih_setelah_pajak,
            'items' => []
        ];

        return $report;
    }

    /**
     * Build section for report
     */
    private function buildSection($title, $categories, $mappings, $startDate, $endDate, $groupSimilar = false)
    {
        $section = [
            'title' => $title,
            'items' => [],
            'total' => 0
        ];

        $processedCategories = [];

        foreach ($categories as $category) {
            if (!isset($mappings[$category])) {
                continue;
            }

            // Group similar categories if requested
            if ($groupSimilar) {
                $baseCategory = $this->getBaseCategory($category);
                if (in_array($baseCategory, $processedCategories)) {
                    continue;
                }
                $processedCategories[] = $baseCategory;

                // Find all similar categories
                $similarCategories = $mappings->keys()->filter(function ($key) use ($baseCategory) {
                    return $this->getBaseCategory($key) === $baseCategory;
                });

                $categoryMappings = collect();
                foreach ($similarCategories as $simCat) {
                    if (isset($mappings[$simCat])) {
                        $categoryMappings = $categoryMappings->merge($mappings[$simCat]);
                    }
                }
            } else {
                $categoryMappings = $mappings[$category];
                $baseCategory = $category;
            }

            $categoryTotal = 0;
            $categoryItems = [];

            foreach ($categoryMappings as $mapping) {
                $balance = $this->getAccountBalance($mapping->kode_akun, $startDate, $endDate);

                if ($balance != 0) {
                    $categoryItems[] = [
                        'kode_akun' => $mapping->kode_akun,
                        'nama_akun' => $mapping->nama_akun,
                        'saldo' => $balance
                    ];
                    $categoryTotal += $balance;
                }
            }

            if ($categoryTotal != 0 || !empty($categoryItems)) {
                $section['items'][] = [
                    'category' => $baseCategory,
                    'total' => $categoryTotal,
                    'accounts' => $categoryItems
                ];
                $section['total'] += $categoryTotal;
            }
        }

        return $section;
    }

    /**
     * Get base category name for grouping
     */
    private function getBaseCategory($category)
    {
        // Group similar "Beban Lain" categories
        if (
            strpos($category, 'Beban Lain') !== false ||
            strpos($category, 'Beban lain') !== false
        ) {
            return 'Lain-lain';
        }

        return $category;
    }

    /**
     * Get account balance for period
     */
    private function getAccountBalance($kodeAkun, $startDate, $endDate)
    {
        return BukuBesar::where('kode_akun', $kodeAkun)
            ->whereBetween('tanggal', [$startDate, $endDate])
            ->sum('debit') - BukuBesar::where('kode_akun', $kodeAkun)
            ->whereBetween('tanggal', [$startDate, $endDate])
            ->sum('kredit');
    }

    /**
     * Format currency for display
     */
    public function formatCurrency($amount)
    {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}
