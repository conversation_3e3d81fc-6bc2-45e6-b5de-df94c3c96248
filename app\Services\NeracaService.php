<?php

namespace App\Services;

use App\Models\NeracaMapping;
use App\Models\JournalEntry;
use App\Models\Akun;
use Carbon\Carbon;

use Illuminate\Database\Eloquent\Builder;

class NeracaService
{
    /**
     * Generate Neraca Report
     */
    public function generateReport($reportDate)
    {
        $reportDate = Carbon::parse($reportDate)->endOfDay();

        // Get all mappings grouped by category
        $mappings = NeracaMapping::getMappingsByCategory();

        // If no mappings exist, return empty structure
        if ($mappings->isEmpty()) {
            $report['aktiva']['lancar'] = ['title' => 'Aktiva Lancar', 'items' => [], 'total' => 0];
            $report['aktiva']['tetap'] = ['title' => 'Aktiva Tetap', 'items' => [], 'total' => 0];
            $report['pasiva']['hutang'] = ['title' => 'Hutang', 'items' => [], 'total' => 0];
            $report['pasiva']['modal'] = ['title' => 'Modal', 'items' => [], 'total' => 0];

            $report['aktiva']['total_lancar'] = 0;
            $report['aktiva']['total_tetap'] = 0;
            $report['aktiva']['total'] = 0;
            $report['pasiva']['total_hutang'] = 0;
            $report['pasiva']['total_modal'] = 0;
            $report['pasiva']['total'] = 0;
            $report['is_balanced'] = true;

            return $report;
        }

        $report = [
            'periode' => [
                'date' => $reportDate->format('d F Y'),
                'title' => 'PERIODE ' . $reportDate->format('d F Y')
            ],
            'aktiva' => [],
            'pasiva' => []
        ];

        // AKTIVA LANCAR
        $aktivaLancarCategories = [
            'Kas & Bank',
            'Piutang Usaha',
            'Piutang Lain-Lain',
            'Persediaan',
            'Biaya Dibayar Dimuka'
        ];

        $report['aktiva']['lancar'] = $this->buildSection(
            'Aktiva Lancar',
            $aktivaLancarCategories,
            $mappings,
            $reportDate
        );

        // AKTIVA TETAP
        $aktivaTetapCategories = [
            'Tanah dan Bangunan',
            'Peralatan & Kendaraan',
            'Akm. Peny. Peralatan & Kendaraan',
            'Inventaris',
            'Akm. Peny. Inventaris'
        ];

        $report['aktiva']['tetap'] = $this->buildSection(
            'Aktiva Tetap',
            $aktivaTetapCategories,
            $mappings,
            $reportDate
        );

        // Calculate Total Aktiva Lancar
        $totalAktivaLancar = $report['aktiva']['lancar']['total'];

        // Calculate Total Aktiva Tetap
        $totalAktivaTetap = $report['aktiva']['tetap']['total'];

        // Total Aktiva
        $totalAktiva = $totalAktivaLancar + $totalAktivaTetap;

        $report['aktiva']['total_lancar'] = $totalAktivaLancar;
        $report['aktiva']['total_tetap'] = $totalAktivaTetap;
        $report['aktiva']['total'] = $totalAktiva;

        // HUTANG
        $hutangCategories = [
            'Hutang Usaha',
            'Hutang Kendaraan',
            'Hutang Pajak',
            'Hutang Bank'
        ];

        $report['pasiva']['hutang'] = $this->buildSection(
            'Hutang',
            $hutangCategories,
            $mappings,
            $reportDate
        );

        // MODAL
        $modalCategories = [
            'Modal Disetor'
        ];

        $report['pasiva']['modal'] = $this->buildSection(
            'Modal',
            $modalCategories,
            $mappings,
            $reportDate
        );

        // Calculate Laba Ditahan s.d 2023 (accumulated earnings until 2023)
        $labaDitahanSd2023 = $this->getLabaDitahanSd2023($reportDate);
        if ($labaDitahanSd2023 != 0) {
            $report['pasiva']['modal']['items'][] = [
                'category' => 'Laba Ditahan s.d 2023',
                'total' => $labaDitahanSd2023,
                'accounts' => [[
                    'kode_akun' => '',
                    'nama_akun' => 'Laba Ditahan s.d 2023',
                    'saldo' => $labaDitahanSd2023
                ]]
            ];
            $report['pasiva']['modal']['total'] += $labaDitahanSd2023;
        }

        // Calculate Laba Tahun Berjalan (current year earnings)
        $labaTahunBerjalan = $this->getLabaTahunBerjalan($reportDate);
        if ($labaTahunBerjalan != 0) {
            $report['pasiva']['modal']['items'][] = [
                'category' => 'Laba Tahun Berjalan',
                'total' => $labaTahunBerjalan,
                'accounts' => [[
                    'kode_akun' => '',
                    'nama_akun' => 'Laba Tahun Berjalan',
                    'saldo' => $labaTahunBerjalan
                ]]
            ];
            $report['pasiva']['modal']['total'] += $labaTahunBerjalan;
        }

        // Calculate totals
        $totalHutang = $report['pasiva']['hutang']['total'];
        $totalModal = $report['pasiva']['modal']['total'];
        $totalPasiva = $totalHutang + $totalModal;

        $report['pasiva']['total_hutang'] = $totalHutang;
        $report['pasiva']['total_modal'] = $totalModal;
        $report['pasiva']['total'] = $totalPasiva;

        // Check if balanced
        $report['is_balanced'] = abs($totalAktiva - $totalPasiva) < 0.01;

        return $report;
    }

    /**
     * Build section for report
     */
    private function buildSection($title, $categories, $mappings, $reportDate)
    {
        $section = [
            'title' => $title,
            'items' => [],
            'total' => 0
        ];

        foreach ($categories as $category) {
            if (!isset($mappings[$category])) {
                continue;
            }

            $categoryMappings = $mappings[$category];
            $categoryTotal = 0;
            $categoryItems = [];

            foreach ($categoryMappings as $mapping) {
                $balance = $this->getAccountBalance($mapping->kode_akun, $reportDate);

                if ($balance != 0) {
                    $categoryItems[] = [
                        'kode_akun' => $mapping->kode_akun,
                        'nama_akun' => $mapping->nama_akun,
                        'saldo' => $balance
                    ];
                    $categoryTotal += $balance;
                }
            }

            if ($categoryTotal != 0 || !empty($categoryItems)) {
                $section['items'][] = [
                    'category' => $category,
                    'total' => $categoryTotal,
                    'accounts' => $categoryItems
                ];
                $section['total'] += $categoryTotal;
            }
        }

        return $section;
    }

    /**
     * Get account balance up to report date
     */
    private function getAccountBalance($kodeAkun, $reportDate)
    {
        // Get account info
        $account = Akun::where('kode_akun', $kodeAkun)->first();
        if (!$account) {
            return 0;
        }

        // Get journal entries for this account up to report date
        $entries = JournalEntry::where('account_id', $account->id)
            ->whereHas('journal', function (Builder $query) use ($reportDate) {
                $query->where('status', 'Posted')
                    ->where('transaction_date', '<=', $reportDate);
            })
            ->get();

        $totalDebit = $entries->sum('debit');
        $totalCredit = $entries->sum('credit');

        // Start with opening balance
        $balance = $account->saldo_awal ?? 0;

        // For asset and expense accounts (debit normal balance)
        if (in_array($account->kategori_akun, ['Aset', 'Beban'])) {
            $balance += $totalDebit - $totalCredit;
        } else {
            // For liability, equity, and revenue accounts (credit normal balance)
            $balance += $totalCredit - $totalDebit;
        }

        return $balance;
    }

    /**
     * Get Laba Ditahan s.d 2023 (accumulated earnings until 2023)
     */
    private function getLabaDitahanSd2023($reportDate)
    {
        $endOf2023 = Carbon::create(2023, 12, 31)->endOfDay();

        // Only calculate if report date is after 2023
        if ($reportDate->year <= 2023) {
            return 0;
        }

        return $this->calculateNetIncome(null, $endOf2023);
    }

    /**
     * Get Laba Tahun Berjalan (current year earnings)
     */
    private function getLabaTahunBerjalan($reportDate)
    {
        $startOfYear = $reportDate->copy()->startOfYear();
        return $this->calculateNetIncome($startOfYear, $reportDate);
    }

    /**
     * Calculate net income for a period
     */
    private function calculateNetIncome($startDate, $endDate)
    {
        // Get revenue accounts
        $revenueAccounts = Akun::where('kategori_akun', 'Pendapatan')->get();
        $expenseAccounts = Akun::where('kategori_akun', 'Beban')->get();

        $totalRevenue = 0;
        $totalExpense = 0;

        foreach ($revenueAccounts as $account) {
            $entries = JournalEntry::where('account_id', $account->id)
                ->whereHas('journal', function (Builder $query) use ($startDate, $endDate) {
                    $query->where('status', 'Posted');
                    if ($startDate) {
                        $query->whereBetween('transaction_date', [$startDate, $endDate]);
                    } else {
                        $query->where('transaction_date', '<=', $endDate);
                    }
                })
                ->get();

            $totalRevenue += $entries->sum('credit') - $entries->sum('debit');
        }

        foreach ($expenseAccounts as $account) {
            $entries = JournalEntry::where('account_id', $account->id)
                ->whereHas('journal', function (Builder $query) use ($startDate, $endDate) {
                    $query->where('status', 'Posted');
                    if ($startDate) {
                        $query->whereBetween('transaction_date', [$startDate, $endDate]);
                    } else {
                        $query->where('transaction_date', '<=', $endDate);
                    }
                })
                ->get();

            $totalExpense += $entries->sum('debit') - $entries->sum('credit');
        }

        return $totalRevenue - $totalExpense;
    }

    /**
     * Format currency for display
     */
    public function formatCurrency($amount)
    {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}
