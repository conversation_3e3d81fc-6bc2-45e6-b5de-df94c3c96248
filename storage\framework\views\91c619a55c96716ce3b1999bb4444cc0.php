<?php
    $record = $getRecord();
    $attributes = $record->attributes ?? [];
    $category = $record->category;

    // Debug: pastikan data ada
    if (empty($attributes)) {
        $attributes = [];
    }

    // Jika attributes adalah string JSON, decode
    if (is_string($attributes)) {
        $attributes = json_decode($attributes, true) ?? [];
    }
?>

<div class="text-xs space-y-1">
    <?php if(!empty($attributes)): ?>
        <?php switch($category):
            case ('ganti_oli'): ?>
                <?php if(isset($attributes['jenis_oli'])): ?>
                    <div><span class="font-medium text-gray-600">Oli:</span> <?php echo e($attributes['jenis_oli']); ?></div>
                <?php endif; ?>
                <?php if(isset($attributes['kilometer'])): ?>
                    <div><span class="font-medium text-gray-600">KM:</span> <?php echo e(number_format($attributes['kilometer'])); ?></div>
                <?php endif; ?>
                <?php if(isset($attributes['bengkel'])): ?>
                    <div><span class="font-medium text-gray-600">Bengkel:</span> <?php echo e($attributes['bengkel']); ?></div>
                <?php endif; ?>
            <?php break; ?>

            <?php case ('vehicle_fuel'): ?>
                <?php if(isset($attributes['jenis_bbm'])): ?>
                    <div><span class="font-medium text-gray-600">BBM:</span> <?php echo e($attributes['jenis_bbm']); ?></div>
                <?php endif; ?>
                <?php if(isset($attributes['liter'])): ?>
                    <div><span class="font-medium text-gray-600">Liter:</span> <?php echo e(number_format($attributes['liter'])); ?>L</div>
                <?php endif; ?>
                <?php if(isset($attributes['spbu'])): ?>
                    <div><span class="font-medium text-gray-600">SPBU:</span> <?php echo e($attributes['spbu']); ?></div>
                <?php endif; ?>
            <?php break; ?>

            <?php case ('delivery_reimbursement'): ?>
                <?php if(isset($attributes['tujuan_delivery'])): ?>
                    <div><span class="font-medium text-gray-600">Tujuan:</span> <?php echo e($attributes['tujuan_delivery']); ?></div>
                <?php endif; ?>
                <?php if(isset($attributes['jarak_tempuh'])): ?>
                    <div><span class="font-medium text-gray-600">Jarak:</span> <?php echo e(number_format($attributes['jarak_tempuh'])); ?>

                        km</div>
                <?php endif; ?>
                <?php if(isset($attributes['no_delivery_order'])): ?>
                    <div><span class="font-medium text-gray-600">DO:</span> <?php echo e($attributes['no_delivery_order']); ?></div>
                <?php endif; ?>
            <?php break; ?>

            <?php case ('uang_jalan_sc'): ?>
                <?php if(isset($attributes['nama_supir'])): ?>
                    <div><span class="font-medium text-gray-600">Supir:</span> <?php echo e($attributes['nama_supir']); ?></div>
                <?php endif; ?>
                <?php if(isset($attributes['rute_perjalanan'])): ?>
                    <div><span class="font-medium text-gray-600">Rute:</span> <?php echo e($attributes['rute_perjalanan']); ?></div>
                <?php endif; ?>
                <?php if(isset($attributes['total_uang_jalan'])): ?>
                    <div><span class="font-medium text-gray-600">Total:</span> Rp
                        <?php echo e(number_format($attributes['total_uang_jalan'])); ?></div>
                <?php endif; ?>
            <?php break; ?>

            <?php default: ?>
                <?php
                    $count = 0;
                    $maxDisplay = 3;
                ?>
                <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($count < $maxDisplay && !empty($value)): ?>
                        <div><span class="font-medium text-gray-600"><?php echo e(ucfirst(str_replace('_', ' ', $key))); ?>:</span>
                            <?php echo e(is_array($value) ? implode(', ', $value) : $value); ?></div>
                        <?php $count++; ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php if(count($attributes) > $maxDisplay): ?>
                    <div class="text-gray-500 italic">+<?php echo e(count($attributes) - $maxDisplay); ?> lainnya</div>
                <?php endif; ?>
        <?php endswitch; ?>
    <?php else: ?>
        <div class="text-gray-400 italic">Tidak ada data tambahan</div>
    <?php endif; ?>
</div>
<?php /**PATH D:\laragon\www\lrp\resources\views\tables\columns\expense-attributes-summary.blade.php ENDPATH**/ ?>