<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <?php echo e($this->form); ?>


            <div class="mt-6 flex justify-end">
                <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','icon' => 'heroicon-o-document-arrow-down','onclick' => 'alert(\'Fitur export PDF akan segera tersedia\')']); ?>
                    Export PDF
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Income Statement Report -->
        <!--[if BLOCK]><![endif]--><?php if($this->start_date && $this->end_date): ?>
            <?php
                $data = $this->getIncomeStatementData();
            ?>

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
                        PT. LINTAS RIAU PRIMA
                    </h2>
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-gray-200">
                        LAPORAN RUGI LABA
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        <?php echo e($data['periode']['title']); ?>

                    </p>
                </div>

                <div class="space-y-6">
                    <!--[if BLOCK]><![endif]--><?php if(isset($data['sections'])): ?>
                        <!-- PEREDARAN USAHA -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['peredaran_usaha'])): ?>
                            <?php $section = $data['sections']['peredaran_usaha']; ?>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    <?php echo e($section['title']); ?>

                                </h3>
                                <div class="space-y-2 ml-4">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $section['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 px-6 ml-4">
                                                    <span class="text-gray-700 dark:text-gray-300 text-sm">
                                                        <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <div class="mt-4 pt-3 border-t-2 border-gray-300 dark:border-gray-600 ml-4">
                                    <div class="flex justify-between items-center font-bold text-lg">
                                        <span class="text-gray-900 dark:text-gray-100">Total <?php echo e($section['title']); ?></span>
                                        <span class="text-blue-600 dark:text-blue-400">
                                            <?php echo e($this->formatCurrency($section['total'])); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- HPP -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['hpp'])): ?>
                            <?php $section = $data['sections']['hpp']; ?>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    <?php echo e($section['title']); ?>

                                </h3>
                                <div class="space-y-2 ml-4">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $section['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 px-6 ml-4">
                                                    <span class="text-gray-700 dark:text-gray-300 text-sm">
                                                        <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <div class="mt-4 pt-3 border-t-2 border-gray-300 dark:border-gray-600 ml-4">
                                    <div class="flex justify-between items-center font-bold text-lg">
                                        <span class="text-gray-900 dark:text-gray-100">Total <?php echo e($section['title']); ?></span>
                                        <span class="text-red-600 dark:text-red-400">
                                            <?php echo e($this->formatCurrency($section['total'])); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- LABA BRUTO -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['laba_bruto'])): ?>
                            <?php $section = $data['sections']['laba_bruto']; ?>
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-blue-50 dark:bg-blue-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        <?php echo e($section['title']); ?>

                                    </span>
                                    <span class="text-xl font-bold <?php echo e($section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                                        <?php echo e($this->formatCurrency($section['total'])); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- BEBAN UMUM / ADM / OPS -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['beban_umum'])): ?>
                            <?php $section = $data['sections']['beban_umum']; ?>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    <?php echo e($section['title']); ?>

                                </h3>
                                <div class="space-y-2 ml-4">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $section['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $category['accounts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex justify-between items-center py-1 px-6 ml-4">
                                                    <span class="text-gray-700 dark:text-gray-300 text-sm">
                                                        <?php echo e($account['nama_akun']); ?>

                                                    </span>
                                                    <span class="text-gray-600 dark:text-gray-400 text-sm">
                                                        <?php echo e($this->formatCurrency($account['saldo'])); ?>

                                                    </span>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <div class="mt-4 pt-3 border-t-2 border-gray-300 dark:border-gray-600 ml-4">
                                    <div class="flex justify-between items-center font-bold text-lg">
                                        <span class="text-gray-900 dark:text-gray-100">Jumlah</span>
                                        <span class="text-red-600 dark:text-red-400">
                                            <?php echo e($this->formatCurrency($section['total'])); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- LABA USAHA -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['laba_usaha'])): ?>
                            <?php $section = $data['sections']['laba_usaha']; ?>
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-green-50 dark:bg-green-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        <?php echo e($section['title']); ?>

                                    </span>
                                    <span class="text-xl font-bold <?php echo e($section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                                        <?php echo e($this->formatCurrency($section['total'])); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- PENDAPATAN DAN BEBAN LUAR USAHA -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['pendapatan_luar_usaha']) || isset($data['sections']['beban_luar_usaha'])): ?>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    PENDAPATAN DAN BEBAN LUAR USAHA
                                </h3>
                                
                                <?php if(isset($data['sections']['pendapatan_luar_usaha'])): ?>
                                    <?php $section = $data['sections']['pendapatan_luar_usaha']; ?>
                                    <div class="ml-4 mb-4">
                                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">- Pendapatan Jasa Giro</h4>
                                        <h4 class="font-medium text-gray-800 dark:text-gray-200 mb-2">- Beban Adm Bank & Pajak Giro</h4>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- LABA LUAR USAHA -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['laba_luar_usaha'])): ?>
                            <?php $section = $data['sections']['laba_luar_usaha']; ?>
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-yellow-50 dark:bg-yellow-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        <?php echo e($section['title']); ?>

                                    </span>
                                    <span class="text-xl font-bold <?php echo e($section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                                        <?php echo e($this->formatCurrency($section['total'])); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- LABA BERSIH SEBELUM PAJAK -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['laba_bersih_sebelum_pajak'])): ?>
                            <?php $section = $data['sections']['laba_bersih_sebelum_pajak']; ?>
                            <div class="pt-4 border-t-2 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-3 bg-purple-50 dark:bg-purple-900 px-6 rounded-lg">
                                    <span class="text-lg font-bold text-gray-900 dark:text-gray-100">
                                        <?php echo e($section['title']); ?>

                                    </span>
                                    <span class="text-xl font-bold <?php echo e($section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                                        <?php echo e($this->formatCurrency($section['total'])); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- PAJAK PENGHASILAN -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['pajak'])): ?>
                            <?php $section = $data['sections']['pajak']; ?>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4 border-b-2 border-gray-300 dark:border-gray-600 pb-2">
                                    Pajak Penghasilan
                                </h3>
                                <div class="space-y-2 ml-4">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $section['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-3">
                                            <div class="flex justify-between items-center py-1 px-3">
                                                <span class="font-medium text-gray-800 dark:text-gray-200"><?php echo e($category['category']); ?></span>
                                                <span class="font-medium text-gray-900 dark:text-gray-100">
                                                    <?php echo e($this->formatCurrency($category['total'])); ?>

                                                </span>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- LABA BERSIH SETELAH PAJAK -->
                        <!--[if BLOCK]><![endif]--><?php if(isset($data['sections']['laba_bersih_setelah_pajak'])): ?>
                            <?php $section = $data['sections']['laba_bersih_setelah_pajak']; ?>
                            <div class="pt-6 border-t-4 border-gray-400 dark:border-gray-600">
                                <div class="flex justify-between items-center py-4 bg-gray-50 dark:bg-gray-700 px-6 rounded-lg">
                                    <span class="text-xl font-bold text-gray-900 dark:text-gray-100">
                                        <?php echo e($section['title']); ?>

                                    </span>
                                    <span class="text-2xl font-bold <?php echo e($section['total'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'); ?>">
                                        <?php echo e($this->formatCurrency($section['total'])); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Signature Section -->
                        <div class="pt-8 mt-8 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex justify-end">
                                <div class="text-center">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-16">
                                        Pekanbaru, <?php echo e(\Carbon\Carbon::parse($this->end_date)->format('d F Y')); ?>

                                    </p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        PT. Lintas Riau Prima,
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div class="text-center py-8">
                    <div class="text-gray-400 dark:text-gray-500 mb-2">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
                        Pilih Periode Laporan
                    </h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        Silakan pilih tanggal mulai dan akhir untuk melihat laporan laba rugi
                    </p>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\lrp\resources\views/filament/pages/income-statement.blade.php ENDPATH**/ ?>